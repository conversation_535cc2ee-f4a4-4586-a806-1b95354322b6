"use client"

import React from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>Provider,
  Tooltip<PERSON>rigger,
} from "@/components/ui/tooltip"

interface TruncatedCellProps {
  content: string | number
  maxWidth?: string
  className?: string
}

export function TruncatedCell({ 
  content, 
  maxWidth = "120px", 
  className = "" 
}: TruncatedCellProps) {
  const stringContent = String(content || "")
  const shouldTruncate = stringContent.length > 15 // Threshold for truncation

  if (!shouldTruncate) {
    return (
      <div className={`${className}`} style={{ maxWidth }}>
        {stringContent}
      </div>
    )
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div 
            className={`truncate cursor-help ${className}`}
            style={{ maxWidth }}
            title={stringContent}
          >
            {stringContent}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <p className="text-sm">{stringContent}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// Utility function to calculate optimal column width based on content
export function calculateColumnWidth(
  data: any[], 
  field: string, 
  minWidth: number = 80, 
  maxWidth: number = 200
): string {
  if (!data || data.length === 0) return `${minWidth}px`

  // Sample first 10 rows to estimate width
  const sampleData = data.slice(0, 10)
  const maxLength = sampleData.reduce((max, item) => {
    const value = String(item[field] || "")
    return Math.max(max, value.length)
  }, 0)

  // Estimate width: ~8px per character + padding
  const estimatedWidth = Math.max(minWidth, Math.min(maxWidth, maxLength * 8 + 16))
  
  return `${estimatedWidth}px`
}

// Column configuration for Purchase Tax table
export const purchaseTableColumns = [
  { field: "index", label: "#", minWidth: 40, maxWidth: 50 },
  { field: "purchase_date", label: "Purchase Date", minWidth: 100, maxWidth: 120 },
  { field: "brand", label: "Brand", minWidth: 70, maxWidth: 100 },
  { field: "model", label: "Model", minWidth: 120, maxWidth: 180 },
  { field: "color", label: "Color", minWidth: 70, maxWidth: 100 },
  { field: "year", label: "Year", minWidth: 50, maxWidth: 70 },
  { field: "old_license_plate", label: "License Plate", minWidth: 90, maxWidth: 120 },
  { field: "vat_percent", label: "VAT %", minWidth: 60, maxWidth: 80 },
  { field: "purchase_price", label: "Purchase Price", minWidth: 100, maxWidth: 130 },
  { field: "purchase_vat_percent", label: "Purchase VAT", minWidth: 100, maxWidth: 130 },
  { field: "operation_cost_incl_vat", label: "Operation Cost", minWidth: 100, maxWidth: 130 },
  { field: "transport_1_auction_lot", label: "Transport 1", minWidth: 100, maxWidth: 130 },
  { field: "initial_check", label: "Initial Check", minWidth: 100, maxWidth: 130 },
  { field: "tax_insurance_cost_zero", label: "Tax Insurance", minWidth: 100, maxWidth: 130 },
  { field: "other_costs_seven", label: "Other Costs", minWidth: 100, maxWidth: 130 },
  { field: "five_three_tax_percentage", label: "5.3% Tax", minWidth: 100, maxWidth: 130 },
  { field: "total_purchase_cost", label: "Total Cost", minWidth: 110, maxWidth: 140 },
]

// Column configuration for Sellout Tax table
export const selloutTableColumns = [
  { field: "index", label: "#", minWidth: 40, maxWidth: 50 },
  { field: "sale_date", label: "Sale Date", minWidth: 100, maxWidth: 120 },
  { field: "finance_received_date", label: "Finance Received Date", minWidth: 120, maxWidth: 150 },
  { field: "car_tax_invoice_date", label: "Car Tax Invoice Date", minWidth: 120, maxWidth: 150 },
  { field: "car_tax_invoice_number", label: "Car Tax Invoice Number", minWidth: 120, maxWidth: 150 },
  { field: "car_amount", label: "Car Amount", minWidth: 100, maxWidth: 130 },
  { field: "car_vat_amount", label: "Car VAT Amount", minWidth: 100, maxWidth: 130 },
  { field: "commission_tax_invoice_date", label: "Commission Tax Invoice Date", minWidth: 140, maxWidth: 170 },
  { field: "commission_tax_invoice_number", label: "Commission Tax Invoice Number", minWidth: 140, maxWidth: 170 },
  { field: "car_commission_amount", label: "Car Commission Amount", minWidth: 120, maxWidth: 150 },
  { field: "input_vat_commission", label: "Input VAT Commission", minWidth: 120, maxWidth: 150 },
  { field: "withholding_tax", label: "Withholding Tax", minWidth: 100, maxWidth: 130 },
  { field: "vat_percent", label: "VAT %", minWidth: 60, maxWidth: 80 },
  { field: "tank_number", label: "Tank #", minWidth: 80, maxWidth: 100 },
]
