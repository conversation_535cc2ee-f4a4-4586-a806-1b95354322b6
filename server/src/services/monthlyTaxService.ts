import MonthlyTaxRepository from "../repositories/monthlyTaxRepository"
import { MonthlyTaxParams, PurchaseTaxData, SelloutTaxData, MonthlyTaxResponse } from "../models/MonthlyTax"

class MonthlyTaxService {
  private monthlyTaxRepository: MonthlyTaxRepository

  constructor(monthlyTaxRepository: MonthlyTaxRepository) {
    this.monthlyTaxRepository = monthlyTaxRepository
  }

  async getMonthlyTaxData(params: MonthlyTaxParams): Promise<MonthlyTaxResponse> {
    try {
      console.log("[MonthlyTaxService.getMonthlyTaxData] 🚀 Starting with params:", params)

      // Get purchase tax data
      const purchaseRawData = await this.monthlyTaxRepository.getPurchaseTaxData(params)
      console.log("[MonthlyTaxService.getMonthlyTaxData] 📦 Raw purchase data count:", purchaseRawData.length)

      const purchaseData = this.processPurchaseTaxData(purchaseRawData)
      console.log("[MonthlyTaxService.getMonthlyTaxData] ✅ Processed purchase data count:", purchaseData.length)

      // Get tax invoice data
      const selloutRawData = await this.monthlyTaxRepository.getTaxInvoiceData(params)
      console.log("[MonthlyTaxService.getMonthlyTaxData] 📦 Raw sellout data count:", selloutRawData.length)

      const selloutData = this.processSelloutTaxData(selloutRawData)
      console.log("[MonthlyTaxService.getMonthlyTaxData] ✅ Processed sellout data count:", selloutData.length)

      // Log sample data for verification
      if (purchaseData.length > 0) {
        console.log("[MonthlyTaxService.getMonthlyTaxData] 📊 Sample purchase record:", {
          id: purchaseData[0].id,
          purchase_date: purchaseData[0].purchase_date,
          brand: purchaseData[0].brand,
          total_cost: purchaseData[0].total_purchase_cost
        })
      }

      return {
        purchaseData,
        selloutData
      }
    } catch (error: any) {
      console.error("[MonthlyTaxService.getMonthlyTaxData] ❌ Error:", error.message)
      throw error
    }
  }

  private processPurchaseTaxData(rawData: any[]): PurchaseTaxData[] {
    console.log("[MonthlyTaxService.processPurchaseTaxData] 🔄 Processing", rawData.length, "records")

    return rawData
      .filter(item => {
        // Ensure we have valid buyin data with purchase_date
        const buyin = item.car_buyin
        if (!buyin || !buyin.purchase_date) {
          console.warn("[MonthlyTaxService.processPurchaseTaxData] ⚠️ Skipping item without purchase_date:", item.car_id)
          return false
        }
        return true
      })
      .map(item => {
        const buyin = item.car_buyin

        // Calculate total purchase cost with proper number handling
        const purchasePrice = parseFloat(buyin.purchase_price) || 0
        const purchaseVatPercent = parseFloat(buyin.purchase_vat_percent) || 0
        const operationCostInclVat = parseFloat(buyin.operation_cost_incl_vat) || 0
        const transport1AuctionLot = parseFloat(buyin.transport_1_auction_lot) || 0
        const initialCheck = parseFloat(buyin.initial_check) || 0
        const taxInsuranceCostZero = parseFloat(buyin.tax_insurance_cost_zero) || 0
        const otherCostsSeven = parseFloat(buyin.other_costs_seven) || 0
        const fiveThreeTaxPercentage = parseFloat(buyin.five_three_tax_percentage) || 0

        const totalPurchaseCost =
          purchasePrice +
          purchaseVatPercent +
          operationCostInclVat +
          transport1AuctionLot +
          initialCheck +
          taxInsuranceCostZero +
          otherCostsSeven +
          fiveThreeTaxPercentage

        const processedItem = {
          id: item.car_id,
          indexNumber: item.index_number || '',
          carStatus: item.car_status || '',
          purchase_date: buyin.purchase_date,
          brand: buyin.brand || '',
          model: buyin.model || '',
          color: buyin.color || '',
          year: parseInt(buyin.year) || 0,
          old_license_plate: item.old_license_plate || '',
          vat_percent: parseFloat(buyin.vat_percent) || 0,
          purchase_price: purchasePrice,
          purchase_vat_percent: purchaseVatPercent,
          operation_cost_incl_vat: operationCostInclVat,
          transport_1_auction_lot: transport1AuctionLot,
          initial_check: initialCheck,
          tax_insurance_cost_zero: taxInsuranceCostZero,
          other_costs_seven: otherCostsSeven,
          five_three_tax_percentage: fiveThreeTaxPercentage,
          total_purchase_cost: totalPurchaseCost
        }

        return processedItem
      })
  }

  private processSelloutTaxData(rawData: any[]): SelloutTaxData[] {
    return rawData.map(item => {
      // Calculate VAT-related values
      const carCommissionAmount = item.car_commission_amount || 0
      const inputVatCommission = carCommissionAmount * 0.07 // 7% of commission amount
      const withholdingTax = carCommissionAmount * 0.03 // 3% withholding tax
      const vatPercent = 7 // Standard VAT for sales

      return {
        id: item.car_id,
        indexNumber: item.index_number || '',
        sale_date: item.sale_date || '',
        finance_received_date: item.finance_received_date || '',
        car_tax_invoice_date: item.car_tax_invoice_date || '',
        car_tax_invoice_number: item.car_tax_invoice_number || '',
        car_amount: item.car_amount || 0,
        car_vat_amount: item.car_vat_amount || 0,
        commission_tax_invoice_date: item.commission_tax_invoice_date || '',
        commission_tax_invoice_number: item.commission_tax_invoice_number || '',
        car_commission_amount: carCommissionAmount,
        input_vat_commission: inputVatCommission,
        withholding_tax: withholdingTax,
        vat_percent: vatPercent,
        tank_number: item.tank_number || ''
      }
    })
  }
}

export default MonthlyTaxService
