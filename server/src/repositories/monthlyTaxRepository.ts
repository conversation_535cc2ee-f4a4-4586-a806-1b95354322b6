import { supabase } from '../config/database'
import { MonthlyTaxParams } from '../models/MonthlyTax'

// Define interfaces for the data we're working with
interface StockInfo {
  car_id: string
  index_number: string
  car_status: string
}



interface FinanceInfo {
  car_id: string
  finance_received_date: string
  car_tax_invoice_date: string
  car_tax_invoice_number: string
  car_amount: number
  car_vat_amount: number
  commission_tax_invoice_date: string
  commission_tax_invoice_number: string
  car_commission_amount: number
  // These will be added from other tables
  index_number?: string
  car_status?: string
  tank_number?: string
  sale_date?: string
}

class MonthlyTaxRepository {
  async getPurchaseTaxData(params: MonthlyTaxParams) {
    const { month, year } = params

    // Create date range for the selected month
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`

    // Calculate the last day of the month correctly
    const nextMonth = month === 12 ? 1 : month + 1
    const nextYear = month === 12 ? year + 1 : year
    const lastDay = new Date(nextYear, nextMonth - 1, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`

    // First, get car_buyin records that match the date criteria
    const { data: buyinData, error: buyinError } = await supabase
      .from('car_buyin')
      .select(`
        car_id,
        purchase_date,
        brand,
        model,
        color,
        year,
        vat_percent,
        purchase_price,
        purchase_vat_percent,
        operation_cost_incl_vat,
        transport_1_auction_lot,
        initial_check,
        tax_insurance_cost_zero,
        other_costs_seven,
        five_three_tax_percentage
      `)
      .gte('purchase_date', startDate)
      .lte('purchase_date', endDate)
      .not('purchase_date', 'is', null)

    if (buyinError) {
      console.error("[MonthlyTaxRepository.getPurchaseTaxData] ❌ Error fetching buyin data:", buyinError.message)
      throw buyinError
    }

    if (!buyinData || buyinData.length === 0) {
      console.log("[MonthlyTaxRepository.getPurchaseTaxData] ⚠️ No purchase data found for the specified period")
      return []
    }

    // Get the car_ids from the filtered buyin data
    const carIds = buyinData.map(item => item.car_id)

    // Now get the corresponding stock info for these cars
    const { data: stockData, error: stockError } = await supabase
      .from('car_stock_info')
      .select(`
        car_id,
        index_number,
        car_status,
        old_license_plate
      `)
      .in('car_id', carIds)

    if (stockError) {
      console.error("[MonthlyTaxRepository.getPurchaseTaxData] ❌ Error fetching stock data:", stockError.message)
      throw stockError
    }

    // Create a map of car_id to stock info for efficient lookup
    const stockMap: Record<string, any> = {}
    stockData?.forEach(stock => {
      stockMap[stock.car_id] = stock
    })

    // Combine the data
    const combinedData = buyinData.map(buyin => {
      const stock = stockMap[buyin.car_id]
      return {
        car_id: buyin.car_id,
        index_number: stock?.index_number || '',
        car_status: stock?.car_status || '',
        old_license_plate: stock?.old_license_plate || '',
        car_buyin: buyin
      }
    })

    return combinedData
  }

  async getTaxInvoiceData(params: MonthlyTaxParams) {
    const { month, year } = params

    // Create date range for the selected month
    const startDate = `${year}-${month.toString().padStart(2, '0')}-01`

    // Calculate the last day of the month correctly
    // In JavaScript, months are 0-indexed (0 = January, 11 = December)
    // To get the last day of a month, we set the date to the 0th day of the next month
    // which gives us the last day of the current month
    const nextMonth = parseInt(month.toString()) + 1
    const lastDay = new Date(year, nextMonth - 1, 0).getDate()
    const endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay}`

    // First, get the finance data for the specified date range
    const { data: financeData, error } = await supabase
      .from('car_finance')
      .select(`
        car_id,
        finance_received_date,
        car_tax_invoice_date,
        car_tax_invoice_number,
        car_amount,
        car_vat_amount,
        commission_tax_invoice_date,
        commission_tax_invoice_number,
        car_commission_amount
      `)
      .gte('car_tax_invoice_date', startDate)
      .lte('car_tax_invoice_date', endDate)

    // Convert to FinanceInfo array
    const data: FinanceInfo[] = financeData || []

    if (error) {
      console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error:", error.message)
      throw error
    }

    // If we have finance data, fetch the related stock info, buyin, and sellout data
    if (data && data.length > 0) {
      const carIds = data.map(item => item.car_id);

      // Get stock info
      const { data: stockData, error: stockError } = await supabase
        .from('car_stock_info')
        .select('car_id, index_number, car_status')
        .in('car_id', carIds);

      if (stockError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching stock data:", stockError.message);
      } else if (stockData) {
        // Create a map of car_id to stock info
        const stockMap: Record<string, StockInfo> = {};
        stockData.forEach(item => {
          stockMap[item.car_id] = item as StockInfo;
        });

        // Add stock info to each finance record
        data.forEach(item => {
          const stockInfo = stockMap[item.car_id];
          if (stockInfo) {
            item.index_number = stockInfo.index_number;
            item.car_status = stockInfo.car_status;
          }
        });
      }

      // Get buyin data (for tank_number)
      const { data: buyinData, error: buyinError } = await supabase
        .from('car_buyin')
        .select('car_id, tank_number')
        .in('car_id', carIds);

      if (buyinError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching buyin data:", buyinError.message);
      } else if (buyinData) {
        // Create a map of car_id to tank_number
        const tankMap: Record<string, string> = {};
        buyinData.forEach(item => {
          tankMap[item.car_id] = item.tank_number || '';
        });

        // Add tank_number to each finance record
        data.forEach(item => {
          item.tank_number = tankMap[item.car_id] || '';
        });
      }

      // Get sellout data (for sale_date)
      const { data: selloutData, error: selloutError } = await supabase
        .from('car_sellout')
        .select('car_id, sale_date')
        .in('car_id', carIds);

      if (selloutError) {
        console.error("[MonthlyTaxRepository.getTaxInvoiceData] ❌ Error fetching sellout data:", selloutError.message);
      } else if (selloutData) {
        // Create a map of car_id to sale_date
        const saleMap: Record<string, string> = {};
        selloutData.forEach(item => {
          saleMap[item.car_id] = item.sale_date || '';
        });

        // Add sale_date to each finance record
        data.forEach(item => {
          item.sale_date = saleMap[item.car_id] || '';
        });
      }
    }

    // Filter to only include sold cars
    const filteredData = data?.filter(item => item.car_status === 'sold') || [];

    return filteredData;
  }
}

export default MonthlyTaxRepository
